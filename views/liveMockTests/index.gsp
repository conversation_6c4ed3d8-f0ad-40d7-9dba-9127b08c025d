<g:render template="/${session['entryController']}/navheader_new"></g:render>

<style>
    body {
        background: #f8f9fa !important;
    }
    main {
        min-height: 75vh;
        margin-top: 2rem;
    }
    .ws_container {
        width: calc(100% - 30%);
        margin: 0 auto;
    }
    .liveMockTests {
        margin-top: 2rem;
        margin-bottom: 3rem;
    }
    .liveMockTests__title {
        font-size: 1.7rem;
        margin-bottom: 2rem;
        text-align: center;
        color: #333;
    }

    /* Modern Tab Navigation */
    .nav-tabs {
        border-bottom: 2px solid #e9ecef;
        margin-bottom: 30px;
        display: flex;
        flex-wrap: wrap;
        gap: 5px;
    }

    .nav-tabs li {
        margin-bottom: -2px;
        border: none;
    }

    .nav-tabs li a {
        border: none;
        border-radius: 8px 8px 0 0;
        padding: 12px 24px;
        color: #6c757d;
        font-weight: 500;
        text-decoration: none;
        transition: all 0.3s ease;
        background: #f8f9fa;
        margin-right: 4px;
        position: relative;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .nav-tabs li a:hover {
        background: #e9ecef;
        color: #495057;
        border: none;
    }

    .nav-tabs li.active a {
        background: #fff;
        color: #007bff;
        border: none;
        border-bottom: 2px solid #007bff;
        font-weight: 600;
    }

    .nav-tabs li a i {
        font-size: 16px;
    }

    .tab-pane {
        min-height: 400px;
    }

    /* Test Cards Grid Layout */
    .tests-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
        gap: 16px;
        margin-top: 16px;
    }

    .test-card {
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
        border: 1px solid rgba(0, 0, 0, 0.12);
        overflow: hidden;
        transition: all 0.3s ease;
        position: relative;
    }

    .test-card:hover {
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
    }

    /* Card Header with Status */
    .test-card-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        padding: 12px 16px;
        border-bottom: 1px solid #e9ecef;
        position: relative;
    }

    .status-badge {
        position: absolute;
        top: 8px;
        right: 12px;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 10px;
        font-weight: 700;
        text-transform: uppercase;
        color: white;
        letter-spacing: 0.3px;
    }

    .status-badge.ongoing {
        background: linear-gradient(135deg, #17a2b8, #138496);
        box-shadow: 0 1px 4px rgba(23, 162, 184, 0.3);
    }

    .status-badge.upcoming {
        background: linear-gradient(135deg, #ffc107, #e0a800);
        box-shadow: 0 1px 4px rgba(255, 193, 7, 0.3);
        color: #212529;
    }

    .status-badge.completed {
        background: linear-gradient(135deg, #28a745, #1e7e34);
        box-shadow: 0 1px 4px rgba(40, 167, 69, 0.3);
    }

    .test-title {
        font-size: 16px;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 4px;
        line-height: 1.3;
        padding-right: 70px;
    }

    .test-id {
        font-size: 11px;
        color: #6c757d;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.3px;
    }

    /* Card Content */
    .test-card-content {
        padding: 16px;
    }

    /* Test Info with Better Visual Hierarchy */
    .test-info {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 12px;
        margin-bottom: 12px;
        padding: 12px;
        background: #f8f9fa;
        border-radius: 6px;
        border: 1px solid #e9ecef;
    }

    .info-item {
        display: flex;
        align-items: center;
        gap: 6px;
        color: #495057;
        font-size: 13px;
        font-weight: 500;
    }

    .info-item i {
        width: 16px;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #007bff;
        color: white;
        border-radius: 50%;
        font-size: 9px;
    }

    .info-item.questions i {
        background: #17a2b8;
    }

    .info-item.time i {
        background: #ffc107;
        color: #212529;
    }

    .info-value {
        font-weight: 600;
        color: #2c3e50;
    }

    /* Date Time Section */
    .test-schedule {
        background: #fff;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        padding: 12px;
        margin-bottom: 12px;
    }

    .schedule-header {
        font-size: 11px;
        font-weight: 600;
        color: #6c757d;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        margin-bottom: 6px;
        display: flex;
        align-items: center;
        gap: 4px;
    }

    .schedule-header i {
        color: #007bff;
        font-size: 10px;
    }

    .schedule-dates {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 6px;
    }

    .date-item {
        text-align: center;
        flex: 1;
        min-width: 100px;
    }

    .date-label {
        font-size: 10px;
        color: #6c757d;
        text-transform: uppercase;
        font-weight: 600;
        margin-bottom: 2px;
    }

    .date-value {
        font-size: 12px;
        font-weight: 600;
        color: #2c3e50;
        line-height: 1.2;
    }

    .date-separator {
        color: #dee2e6;
        font-size: 14px;
        font-weight: 300;
    }

    /* Language Flags */
    .language-section {
        margin-bottom: 12px;
    }

    .language-header {
        font-size: 10px;
        font-weight: 600;
        color: #6c757d;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        margin-bottom: 6px;
    }

    .language-flags {
        display: flex;
        gap: 6px;
        flex-wrap: wrap;
    }

    .language-flag {
        display: flex;
        align-items: center;
        gap: 4px;
        padding: 4px 8px;
        background: linear-gradient(135deg, #e9ecef, #f8f9fa);
        border: 1px solid #dee2e6;
        border-radius: 12px;
        font-size: 11px;
        color: #495057;
        font-weight: 500;
    }

    .language-flag i {
        color: #007bff;
        font-size: 9px;
    }

    /* Action Button */
    .test-action {
        width: 100%;
        margin-top: auto;
    }

    .action-btn {
        width: 100%;
        padding: 10px 16px;
        border: none;
        border-radius: 6px;
        font-size: 13px;
        font-weight: 700;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        position: relative;
        overflow: hidden;
    }

    .action-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .action-btn:hover::before {
        left: 100%;
    }

    .btn-start-now {
        background: linear-gradient(135deg, #17a2b8, #138496);
        color: white;
        box-shadow: 0 2px 8px rgba(23, 162, 184, 0.3);
        text-decoration: none;
    }

    .btn-start-now:hover {
        background: linear-gradient(135deg, #138496, #117a8b);
        color: white;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(23, 162, 184, 0.4);
        text-decoration: none !important;
    }

    .btn-register {
        background: linear-gradient(135deg, #28a745, #1e7e34);
        color: white;
        box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
    }

    .btn-register:hover {
        background: linear-gradient(135deg, #1e7e34, #155724);
        color: white;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
    }

    .btn-view-results {
        background: linear-gradient(135deg, #6c757d, #5a6268);
        color: white;
        box-shadow: 0 2px 8px rgba(108, 117, 125, 0.3);
    }

    .btn-view-results:hover {
        background: linear-gradient(135deg, #5a6268, #495057);
        color: white;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(108, 117, 125, 0.4);
    }

    .btn-disabled {
        background: #f8f9fa;
        color: #6c757d;
        cursor: not-allowed;
        border: 1px dashed #dee2e6;
        box-shadow: none;
    }

    .btn-disabled:hover {
        transform: none;
        box-shadow: none;
    }

    /* Loading and Empty States */
    .loading-spinner {
        text-align: center;
        padding: 60px 20px;
        color: #6c757d;
    }

    .no-tests {
        text-align: center;
        padding: 60px 20px;
        color: #6c757d;
    }

    .no-tests h4 {
        margin-bottom: 10px;
        color: #495057;
    }

    /* Pagination */
    .pagination {
        justify-content: center;
        margin-top: 30px;
    }

    .pagination li a,
    .pagination li span {
        padding: 8px 12px;
        margin: 0 2px;
        border: 1px solid #dee2e6;
        color: #007bff;
        text-decoration: none;
        border-radius: 4px;
    }

    .pagination li.active span {
        background: #007bff;
        color: white;
        border-color: #007bff;
    }

    .pagination li.disabled span {
        color: #6c757d;
        background: #fff;
        border-color: #dee2e6;
    }

    /* Responsive */
    @media (max-width: 768px) {
        main {
            min-height: 75vh;
            margin-top: 1.75rem;
        }
        .ws_container {
            width: calc(100% - 4%);
        }
        .tests-grid {
            grid-template-columns: 1fr;
            gap: 12px;
        }
        .test-info {
            grid-template-columns: 1fr;
            gap: 8px;
            padding: 10px;
        }
        .nav-tabs li a{
            padding: 10px 6px;
            gap: 4px;
        }
        .test-title {
            font-size: 15px;
            padding-right: 60px;
        }
        .test-card-header {
            padding: 10px 12px;
        }
        .test-card-content {
            padding: 12px;
        }
        .test-schedule {
            padding: 10px;
            margin-bottom: 10px;
        }
        .schedule-dates {
            flex-direction: column;
            gap: 8px;
        }
        .date-separator {
            transform: rotate(90deg);
            font-size: 12px;
        }
        .action-btn {
            padding: 10px 14px;
            font-size: 12px;
        }
        .language-section {
            margin-bottom: 10px;
        }
    }
    .tabpanel{
        padding: 0 !important;
    }
    @media (max-width: 480px) {
        .tests-grid {
            grid-template-columns: 1fr;
            gap: 10px;
        }
        .test-card-content {
            padding: 10px;
        }
        .test-card-header {
            padding: 8px 10px;
        }
        .test-title {
            font-size: 14px;
            padding-right: 55px;
        }
        .status-badge {
            padding: 3px 6px;
            font-size: 9px;
            top: 6px;
            right: 8px;
        }
        .test-info {
            padding: 8px;
            gap: 6px;
            margin-bottom: 8px;
        }
        .test-schedule {
            padding: 8px;
            margin-bottom: 8px;
        }
        .info-item {
            font-size: 12px;
        }
        .info-item i {
            width: 14px;
            height: 14px;
            font-size: 8px;
        }
        .language-section {
            margin-bottom: 8px;
        }
        .language-flag {
            padding: 3px 6px;
            font-size: 10px;
        }
        .action-btn {
            padding: 8px 12px;
            font-size: 11px;
        }
    }
</style>

<main>
    <div class="ws_container">
        <section class="liveMockTests">
            <h1 class="liveMockTests__title">Live Mock Tests</h1>

            <!-- Tab Navigation -->
            <ul class="nav nav-tabs" role="tablist">
                <li role="presentation" class="active">
                    <a href="#ongoing-tests" aria-controls="ongoing-tests" role="tab" onclick="switchTab(event, 'ongoing')" data-tab="ongoing">
                        <i class="fa fa-play-circle"></i> On-Going
                    </a>
                </li>
                <li role="presentation">
                    <a href="#upcoming-tests" aria-controls="upcoming-tests" role="tab" onclick="switchTab(event, 'upcoming')" data-tab="upcoming">
                        <i class="fa-solid fa-hourglass-start"></i> Up-coming
                    </a>
                </li>
                <li role="presentation">
                    <a href="#completed-tests" aria-controls="completed-tests" role="tab" onclick="switchTab(event, 'completed')" data-tab="completed">
                        <i class="fa fa-check-circle"></i> Completed
                    </a>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content">
                <!-- Ongoing Tests Tab -->
                <div role="tabpanel" class="tab-pane in active" id="ongoing-tests">
                    <div id="ongoing-tests-content" class="tests-content">
                        <div class="loading-spinner">
                            <i class="fa fa-spinner fa-spin fa-2x"></i>
                            <p>Loading ongoing tests...</p>
                        </div>
                    </div>
                    <div id="ongoing-tests-pagination"></div>
                </div>

                <!-- Upcoming Tests Tab -->
                <div role="tabpanel" class="tab-pane" id="upcoming-tests">
                    <div id="upcoming-tests-content" class="tests-content">
                        <div class="loading-spinner">
                            <i class="fa fa-spinner fa-spin fa-2x"></i>
                            <p>Loading upcoming tests...</p>
                        </div>
                    </div>
                    <div id="upcoming-tests-pagination"></div>
                </div>

                <!-- Completed Tests Tab -->
                <div role="tabpanel" class="tab-pane" id="completed-tests">
                    <div id="completed-tests-content" class="tests-content">
                        <div class="loading-spinner">
                            <i class="fa fa-spinner fa-spin fa-2x"></i>
                            <p>Loading completed tests...</p>
                        </div>
                    </div>
                    <div id="completed-tests-pagination"></div>
                </div>
            </div>
        </section>
    </div>
</main>

<script>
document.addEventListener('DOMContentLoaded', function() {
    var currentTab = 'ongoing';
    var currentPage = {
        ongoing: 1,
        upcoming: 1,
        completed: 1,
    };
    var pageSize = 10;

    // Initialize the page
    loadTests('ongoing', 1);

    // Function to switch tabs
    window.switchTab = function(event, tabType) {
        event.preventDefault();

        // Update tab navigation
        var tabs = document.querySelectorAll('.nav-tabs li');
        var tabPanes = document.querySelectorAll('.tab-pane');

        tabs.forEach(function(tab) {
            tab.classList.remove('active');
        });

        tabPanes.forEach(function(pane) {
            pane.classList.remove('active', 'in');
        });

        // Activate current tab
        event.target.closest('li').classList.add('active');
        document.getElementById(tabType + '-tests').classList.add('active', 'in');

        currentTab = tabType;
        loadTests(tabType, currentPage[tabType]);
    };

    // Function to load tests based on tab type and page
    async function loadTests(tabType, page) {
        if (!page) page = 1;

        var contentId = tabType + '-tests-content';
        var paginationId = tabType + '-tests-pagination';

        // Show loading spinner
        var contentElement = document.getElementById(contentId);
        contentElement.innerHTML = '<div class="loading-spinner">' +
            '<i class="fa fa-spinner fa-spin fa-2x"></i>' +
            '<p>Loading ' + tabType + ' tests...</p>' +
            '</div>';

        // Clear pagination
        document.getElementById(paginationId).innerHTML = '';

        var apiUrl;
        switch(tabType) {
            case 'ongoing':
                apiUrl = '${createLink(controller: 'liveMockTests', action: 'getOngoingMockTests')}';
                break;
            case 'upcoming':
                apiUrl = '${createLink(controller: 'liveMockTests', action: 'getUpcomingMockTests')}';
                break;
            case 'completed':
                apiUrl = '${createLink(controller: 'liveMockTests', action: 'getCompletedMockTests')}';
                break;
        }

        try {
            // Use fetch with async/await
            const response = await fetch(apiUrl + '?max=' + pageSize + '&page=' + page, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error('Network response was not ok: ' + response.status);
            }

            const data = await response.json();

            if (data.status === 'success') {
                renderTests(data.data, contentId);
                renderPagination(data.pagination, paginationId, tabType);
                currentPage[tabType] = page;
            } else {
                showError(contentId, data.message || 'Failed to load tests');
            }
        } catch (error) {
            console.error('Error loading tests:', error);
            showError(contentId, 'Failed to load tests. Please try again.');
        }
    }

    // Function to render tests
    function renderTests(tests, contentId) {
        var contentElement = document.getElementById(contentId);

        if (!tests || tests.length === 0) {
            contentElement.innerHTML = '<div class="no-tests">' +
                '<i class="fa fa-inbox fa-3x" style="color: #dee2e6; margin-bottom: 15px;"></i>' +
                '<h4>No tests found</h4>' +
                '<p>There are no tests available in this category.</p>' +
                '</div>';
            return;
        }

        var html = '<div class="tests-grid">';
        for (var i = 0; i < tests.length; i++) {
            html += renderTestCard(tests[i]);
        }
        html += '</div>';

        contentElement.innerHTML = html;
    }

    // Function to render individual test card
    function renderTestCard(test) {
        // Format dates
        var startDate = test.testStartDate ? formatDate(new Date(test.testStartDate)) : 'Not set';
        var endDate = test.testEndDate ? formatDate(new Date(test.testEndDate)) : 'Not set';
        var resultDate = test.testResultDate ? formatDate(new Date(test.testResultDate)) : null;

        // Format dates for schedule section
        var startDateShort = test.testStartDate ? formatDateShort(new Date(test.testStartDate)) : 'TBD';
        var endDateShort = test.testEndDate ? formatDateShort(new Date(test.testEndDate)) : 'TBD';
        var startTimeShort = test.testStartDate ? formatTimeShort(new Date(test.testStartDate)) : '';
        var endTimeShort = test.testEndDate ? formatTimeShort(new Date(test.testEndDate)) : '';

        // Language flags
        var languageFlags = '';
        if (test.language1 || test.language2) {
            if (test.language1) {
                languageFlags += '<div class="language-flag">' +
                    '<i class="fa-solid fa-language"></i>' + test.language1 +
                    '</div>';
            }
            if (test.language2) {
                languageFlags += '<div class="language-flag">' +
                    '<i class="fa-solid fa-language"></i>' + test.language2 +
                    '</div>';
            }
        }

        var languageSection = languageFlags ?
            '<div class="language-section">' +
                '<div class="language-header">Available Languages</div>' +
                '<div class="language-flags">' + languageFlags + '</div>' +
            '</div>' : '';

        // Action button based on status and access
        var actionButton = '';
        var buttonClass = '';
        var buttonText = '';
        var buttonIcon = '';

        if (test.status === 'ongoing') {
            buttonClass = 'btn-start-now';
            buttonText = 'Start Test';
            buttonIcon = '<i class="fa-solid fa-play"></i>';
            actionButton = '<a href="#" class="action-btn ' + buttonClass + '" onclick="startTest(' + test.mcqResId + ', ' + test.quizId + ')">' + buttonIcon + buttonText + '</a>';
        } else if (test.status === 'upcoming') {
            buttonClass = 'btn-disabled';
            buttonText = 'Starts Soon';
            buttonIcon = '<i class="fa-solid fa-hourglass-start"></i>';
            actionButton = '<span class="action-btn ' + buttonClass + '" title="Test will start on ' + startDate + '">' + buttonIcon + buttonText + '</span>';
        } else if (test.status === 'completed') {
            buttonClass = 'btn-view-results';
            buttonText = 'View Results';
            buttonIcon = '<i class="fa-solid fa-chart-line"></i>';
            actionButton = '<a href="#" class="action-btn ' + buttonClass + '" onclick="viewResults(' + test.mcqResId + ', ' + test.quizId + ')">' + buttonIcon + buttonText + '</a>';
        }

        return  '<div class="test-card ' + test.status + '">' +
            '<div class="test-card-header">' +
                '<div class="test-title">' + (test.resourceName || 'Untitled Test') + '</div>' +
                '<div class="status-badge ' + test.status + '">' + test.status + '</div>' +
            '</div>' +
            '<div class="test-card-content">' +
                '<div class="test-info">' +
                    '<div class="info-item questions">' +
                        '<i class="fa-solid fa-question"></i>' +
                        '<span><span class="info-value">' + (test.mcqCount || 0) + '</span> Questions</span>' +
                    '</div>' +
                    (test.totalTime && test.totalTime > 0 ?
                        '<div class="info-item time">' +
                        '<i class="fa-solid fa-clock"></i>' +
                        '<span><span class="info-value">' + (test.totalTime || 0) + '</span> Minutes</span>' +
                        '</div>'
                    : '<div class="info-item time">' +
                        '<i class="fa-solid fa-clock"></i>' +
                        '<span><span class="info-value">No Limit</span></span>' +
                        '</div>') +
                '</div>' +
                '<div class="test-schedule">' +
                    '<div class="schedule-header">' +
                        '<i class="fa-solid fa-calendar-alt"></i>' +
                        'Test Schedule' +
                    '</div>' +
                    '<div class="schedule-dates">' +
                        '<div class="date-item">' +
                            '<div class="date-label">Start</div>' +
                            '<div class="date-value">' + startDateShort + '<br>' + startTimeShort + '</div>' +
                        '</div>' +
                        '<div class="date-separator">→</div>' +
                        '<div class="date-item">' +
                            '<div class="date-label">End</div>' +
                            '<div class="date-value">' + endDateShort + '<br>' + endTimeShort + '</div>' +
                        '</div>' +
                    '</div>' +
                '</div>' +
                languageSection +
                '<div class="test-action">' + actionButton + '</div>' +
            '</div>' +
        '</div>';
    }

    // Function to render pagination
    function renderPagination(pagination, paginationId, tabType) {
        var paginationElement = document.getElementById(paginationId);

        if (!pagination || pagination.totalPages <= 1) {
            paginationElement.innerHTML = '';
            return;
        }

        var currentPageNum = pagination.currentPage;
        var totalPages = pagination.totalPages;

        var paginationHtml = '<ul class="pagination">';

        // Page info
        paginationHtml += '<li class="disabled"><span>Page ' + currentPageNum + ' of ' + totalPages + '</span></li>';

        // Previous button
        if (currentPageNum > 1) {
            paginationHtml += '<li><a href="#" onclick="changePage(' + (currentPageNum - 1) + ', \'' + tabType + '\')">&laquo;</a></li>';
        } else {
            paginationHtml += '<li class="disabled"><span>&laquo;</span></li>';
        }

        // Page numbers
        var startPage = Math.max(1, currentPageNum - 2);
        var endPage = Math.min(totalPages, currentPageNum + 2);

        for (var i = startPage; i <= endPage; i++) {
            if (i === currentPageNum) {
                paginationHtml += '<li class="active"><span>' + i + '</span></li>';
            } else {
                paginationHtml += '<li><a href="#" onclick="changePage(' + i + ', \'' + tabType + '\')">' + i + '</a></li>';
            }
        }

        // Next button
        if (currentPageNum < totalPages) {
            paginationHtml += '<li><a href="#" onclick="changePage(' + (currentPageNum + 1) + ', \'' + tabType + '\')">&raquo;</a></li>';
        } else {
            paginationHtml += '<li class="disabled"><span>&raquo;</span></li>';
        }

        paginationHtml += '</ul>';
        paginationElement.innerHTML = paginationHtml;
    }

    // Handle pagination clicks
    window.changePage = function(page, tabType) {
        if (page && tabType) {
            loadTests(tabType, page);
        }
    };

    // Function to show error message
    function showError(contentId, message) {
        var contentElement = document.getElementById(contentId);
        contentElement.innerHTML = '<div class="no-tests">' +
            '<i class="fa fa-exclamation-triangle fa-3x" style="color: #dc3545; margin-bottom: 15px;"></i>' +
            '<h4>Error</h4>' +
            '<p>' + message + '</p>' +
            '<button class="btn btn-primary" onclick="loadTests(\'' + currentTab + '\', ' + currentPage[currentTab] + ')">' +
                '<i class="fa fa-refresh"></i> Retry' +
            '</button>' +
            '</div>';
    }

    // Utility function to format date
    function formatDate(date) {
        if (!date) return 'Not set';
        var options = {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        };
        return date.toLocaleDateString('en-US', options);
    }

    // Utility function to format date (short version)
    function formatDateShort(date) {
        if (!date) return 'TBD';
        var options = {
            month: 'short',
            day: 'numeric'
        };
        return date.toLocaleDateString('en-US', options);
    }

    // Utility function to format time (short version)
    function formatTimeShort(date) {
        if (!date) return '';
        var options = {
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
        };
        return date.toLocaleTimeString('en-US', options);
    }

    // Function to start a test
    window.startTest = function(mcqResId, quizId) {
        <sec:ifNotLoggedIn>
            loginOpen();
        </sec:ifNotLoggedIn>
        <sec:ifLoggedIn>
            const quizUrl = "/prepjoy/prepJoyGame?quizId="+quizId+"&resId="+mcqResId+"&quizType=testSeries&source=web&siteName=${session['siteName']}&learn=false&pubDesk=false&dailyTest=false&fromLiveMockTest=true";
            setTimeout(() => {
                window.open(quizUrl, '_blank');
            })
        </sec:ifLoggedIn>
    };

    // Function to view test results
    window.viewResults = function(mcqResId, quizId) {
        console.log('Viewing results for mcqResId:', mcqResId);
        alert('Results view functionality to be implemented. MCQ Resource ID: ' + mcqResId);
    };
});
</script>

<g:render template="/${session['entryController']}/footer_new"></g:render>